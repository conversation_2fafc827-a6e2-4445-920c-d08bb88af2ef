//@version=5
indicator("Auto Levels with Alerts", overlay=true)

// ---------------- Inputs ---------------- //
levels_to_show = input.int(3, "Number of Levels", minval=1)
only_today = input.bool(false, "Only Today's Levels")
alert_enabled = input.bool(true, "Enable Alerts")

// ---------------- Trading Session Options ---------------- //
session_group = "Trading Sessions"
enable_sessions = input.bool(false, "Enable Session Filter", group=session_group)
session_type = input.string("All Sessions", "Session Type", options=["All Sessions", "Asian", "European", "American", "Japanese"], group=session_group)
show_session_bg = input.bool(false, "Show Session Background", group=session_group)

// Session time definitions (in UTC)
asian_session = input.session("2300-0800", "Asian Session (Tokyo/Sydney)", group=session_group)
european_session = input.session("0700-1600", "European Session (London)", group=session_group)
american_session = input.session("1330-2000", "American Session (New York)", group=session_group)
japanese_session = input.session("2300-0800", "Japanese Session (Tokyo)", group=session_group)

// ---------------- Session Functions ---------------- //
// Function to check if current time is in selected session
is_in_session() =>
    if not enable_sessions
        true
    else
        switch session_type
            "Asian" => time(timeframe.period, asian_session)
            "European" => time(timeframe.period, european_session)
            "American" => time(timeframe.period, american_session)
            "Japanese" => time(timeframe.period, japanese_session)
            => true  // All Sessions

// Session background colors
get_session_color() =>
    if not show_session_bg or not enable_sessions
        na
    else
        switch session_type
            "Asian" => time(timeframe.period, asian_session) ? color.new(color.yellow, 95) : na
            "European" => time(timeframe.period, european_session) ? color.new(color.blue, 95) : na
            "American" => time(timeframe.period, american_session) ? color.new(color.green, 95) : na
            "Japanese" => time(timeframe.period, japanese_session) ? color.new(color.red, 95) : na
            => na

// Apply session background
bgcolor(get_session_color())

// ---------------- Helper Functions ---------------- //
// Get sorted value from array
get_sorted(arr, order, idx) =>
    if array.size(arr) > idx
        sorted = array.copy(arr)
        array.sort(sorted, order)
        array.get(sorted, idx)
    else
        na

// Price touch detection
priceTouch(level) =>
    ta.crossover(close, level) or ta.crossunder(close, level)

// Color gradient
getColor(baseColor, idx, total) =>
    color.new(baseColor, math.round(50 * idx / total))

// Label function
label_(level, lvlColor, txt) =>
    if is_in_session()
        label.new(bar_index, level, txt, yloc=yloc.price, style=label.style_label_up, color=lvlColor, textcolor=color.white, size=size.tiny)

// Alert message formatter
getAlertMessage(type, level) =>
    emoji = type == "High" ? "📈" : "📉"
    str.format("{0} {1} touched at {2} on {3}", emoji, type, str.tostring(level), str.tostring(time, "dd-MMM HH:mm"))

// ---------------- Arrays to track highs/lows ---------------- //
var float[] highs = array.new_float()
var float[] lows = array.new_float()
var int[] bars_recorded = array.new_int()

// ---------------- Capture Highs & Lows ---------------- //
if (not only_today or dayofmonth(time) == dayofmonth(time)) and is_in_session()
    array.push(highs, high)
    array.push(lows, low)
    array.push(bars_recorded, dayofmonth(time))

// ---------------- Calculate Levels ---------------- //
// Get the levels outside of any loops
hh1 = get_sorted(highs, order.ascending, 0)
hh2 = levels_to_show > 1 ? get_sorted(highs, order.ascending, 1) : na
hh3 = levels_to_show > 2 ? get_sorted(highs, order.ascending, 2) : na

ll1 = get_sorted(lows, order.descending, 0)
ll2 = levels_to_show > 1 ? get_sorted(lows, order.descending, 1) : na
ll3 = levels_to_show > 2 ? get_sorted(lows, order.descending, 2) : na

// ---------------- Plot Levels (must be in global scope) ---------------- //
session_suffix = enable_sessions ? " (" + session_type + ")" : ""
plot(hh1, "High1" + session_suffix, color=getColor(color.red, 0, levels_to_show), style=plot.style_linebr)
plot(hh2, "High2" + session_suffix, color=getColor(color.red, 1, levels_to_show), style=plot.style_linebr)
plot(hh3, "High3" + session_suffix, color=getColor(color.red, 2, levels_to_show), style=plot.style_linebr)

plot(ll1, "Low1" + session_suffix, color=getColor(color.green, 0, levels_to_show), style=plot.style_linebr)
plot(ll2, "Low2" + session_suffix, color=getColor(color.green, 1, levels_to_show), style=plot.style_linebr)
plot(ll3, "Low3" + session_suffix, color=getColor(color.green, 2, levels_to_show), style=plot.style_linebr)

// ---------------- Labels and Alerts ---------------- //
if not na(hh1)
    label_(hh1, getColor(color.red, 0, levels_to_show), "H1")
    if alert_enabled
        alertcondition(priceTouch(hh1),
                       title="Price touched High 1",
                       message=getAlertMessage("High", hh1))

if not na(hh2) and levels_to_show > 1
    label_(hh2, getColor(color.red, 1, levels_to_show), "H2")
    if alert_enabled
        alertcondition(priceTouch(hh2),
                       title="Price touched High 2",
                       message=getAlertMessage("High", hh2))

if not na(hh3) and levels_to_show > 2
    label_(hh3, getColor(color.red, 2, levels_to_show), "H3")
    if alert_enabled
        alertcondition(priceTouch(hh3),
                       title="Price touched High 3",
                       message=getAlertMessage("High", hh3))

if not na(ll1)
    label_(ll1, getColor(color.green, 0, levels_to_show), "L1")
    if alert_enabled
        alertcondition(priceTouch(ll1),
                       title="Price touched Low 1",
                       message=getAlertMessage("Low", ll1))

if not na(ll2) and levels_to_show > 1
    label_(ll2, getColor(color.green, 1, levels_to_show), "L2")
    if alert_enabled
        alertcondition(priceTouch(ll2),
                       title="Price touched Low 2",
                       message=getAlertMessage("Low", ll2))

if not na(ll3) and levels_to_show > 2
    label_(ll3, getColor(color.green, 2, levels_to_show), "L3")
    if alert_enabled
        alertcondition(priceTouch(ll3),
                       title="Price touched Low 3",
                       message=getAlertMessage("Low", ll3))
