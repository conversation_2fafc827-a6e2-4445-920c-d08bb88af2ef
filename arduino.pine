 @version=5
indicator("Pivot Point OHLC Buy/Sell", overlay=true , max_bars_back = 500 , max_boxes_count = 500  , max_labels_count = 500 , max_lines_count = 500 )


import mokshtradingart/number_of_bars_in_a_day/1 as m 


[ number_of_bars ] = m.function_bar_calculation( "D" )


plot(number_of_bars )






on_off_newline  = input.bool(true  , "on/off current day lines / label / zones")
on_off_plots    = input.bool(false , "on/off historical lines")
hide_z          = input.bool(false , "on/off historical zone")
next_day_levels = input.bool(false , "next day levels")








fynction_line( series_ , color_ , width_ ) => 
    if ta.change( series_ ) and on_off_newline  and  on_off_newline == true 
        newLine = line.new(bar_index , series_ , bar_index + number_of_bars , series_ , extend = extend.none , color = color_ , width = width_ )
        line.delete(newLine[1])
    






day_change = ta.change(time("D"))
xh1 =  nz(ta.barssince( day_change ))
xh2 = int( xh1 + 2 )
xh = xh2 / 2


plot(xh1 )






fgh(se ) => 
    hn =  (se == se[1] and on_off_plots ? (se != se[1] ? na : se  ) : na )  
    hn




///////////////


bnm = bar_index + ( number_of_bars / 2 )




label_( series_ , color_ , string se ) => 
    if timeframe.change("D")  and  on_off_newline == true 
        WlabelWL1 = label.new( bnm , series_ , ''+se+'', xloc.bar_index , yloc.price , color = color_ , style=label.style_label_center , textcolor= color.white , size=size.normal, textalign=text.align_center )
        label.delete(WlabelWL1[1])


line_ = 1 




// hide_p = input.bool(false , "on/off candalstick pattern")
// hide_hl = input.bool(false , "on/off high//loww")




ires = "D"
moksh(ires) =>
    day_change = ta.change(time(ires))
    xh = ( nz(ta.barssince( day_change )) + 1 )
    al = ta.highest(high , (xh))
    bl = ta.lowest(low , (xh))


    var float highh1 = 0.0 
    var float loww1  = 0.0 
    var float closee  = 0.0 




    var float highh2 = 0.0 
    var float loww2  = 0.0 


    var float highh3 = 0.0 
    var float loww3  = 0.0 


    if ta.change(time(ires)) 
        highh1 := al[1] 
        loww1  := bl[1]
        closee := close[1]




    [al , bl , highh1 , loww1  , closee ]


[al , bl , highh , loww , closee ] = moksh(ires) 




// cd_highh =  plot( (al) , color = color.red , style = plot.style_linebr , linewidth = 2  )
// cd_loww =  plot( (bl) , color = color.green , style = plot.style_linebr , linewidth = 2  )


pp_period = input.string(title='Period', defval='Day', options=['Day', 'Week', 'Month', 'Year'])
pp_res = pp_period == 'Day' ? 'D' : pp_period == 'Week' ? 'W' : pp_period == 'Month' ? 'M' : 'Y'
// Function outputs 1 when it's the first bar of the D/W/M/Y








is_newbar(res) =>
    ch = 0
    if res == 'Y'
        t = year(time('D'))
        ch := ta.change(t) != 0 ? 1 : 0
        ch
    else
        t = time(res)
        ch := ta.change(t) != 0 ? 1 : 0
        ch
    ch
// Calc Close
pclose = 0.0
pclose := is_newbar(pp_res) ? close[1] : pclose[1]


nonRepaintingClose = pclose


c1 = plot( fgh(nonRepaintingClose) , color = color.white , style = plot.style_linebr  )


cxv = closee








h(num) =>
    day_change = ta.change(time("D"))
    xh = ( nz(ta.barssince( day_change )) + 1 )
    al = ta.highest(high , (xh))
    bl = ta.lowest(low , (xh))


    var float highh = 100000.0 
    var float loww  = 0.0 


    if ta.change(time("D")) 
        highh := al[1] 
        loww  := bl[1]




    var float x = 0.0 


    if ta.change(highh)
        x += 1 


    if x[1] == 50
        x := 1 




    var float h1 = 100000.0 


    if x == num and ta.change(highh)
        h1 := highh 


    if ta.crossover(high , h1 ) 
        h1 := 100000.0 
    h1


////////////////


h1 = h(1) , h2 = h(2) , h3 =  h(3) , h4 = h(4) , h5 = h(5) ,h6 = h(6) , h7 = h(7) , h8 = h(8) , h9 = h(9) , h10 = h(10) 
h11 = h( 11) , h12= h( 12) ,h13 =  h(13 ) ,h14= h( 14) , h15=h(15 ) , h16=h(16 ) , h17=h(17 ) ,h18= h(18 ) , h19=h( 19) , h20=h(20 ) 
h21 =  h( 21) ,h22 =  h( 22) ,h23= h(23 ) ,h24= h(24 ) ,h25= h(25 ) , h26=h(26 ) , h27= h( 27) , h28=h(28 ) , h29=h( 29) , h30=h(30 )  
h31=h( 31) ,h32= h( 32) ,h33= h( 33) ,h34= h(34 ) ,h35= h(35 ) ,h36= h(36 ) , h37=h(37 ) , h38=h( 38) , h39=h( 39) , h40=h( 40)  
h41=h( 41) , h42=h( 42) , h43=h( 43) , h44=h(44 ) , h45=h( 45) , h46=h(46 ) , h47=h( 47) , h48=h(48 ) , h49=h(49 ) , h50=h( 50)  
h51 = h( 51) , h52 = h( 52) , h53=h( 53) , h54=h( 54) , h55=h( 55) , h56=h( 56) , h57=h(57 ) , h58=h( 58) , h59=h( 59) , h60=h( 60)  ,
h61= h( 61) , h62=h( 62) , h63=h( 63) , h64=h( 64) , h65=h(65 ) , h66=h( 66) , h67=h(67 ) , h68=h(68 ) , h69=h( 69) , h70=h( 70)  ,
h71=h( 71) , h72=h( 72) , h73=h( 73) , h74=h( 74) , h75=h(75 ) , h76=h(76 ) , h77=h( 77) , h78=h(78)  , h79=h(79)  , h80=h( 80)  ,
h81=h(81 ) , h82=h( 82) , h83=h( 83) , h84=h( 84) , h85=h( 85) , h86=h(86 ) , h87=h( 87) , h88=h( 88) , h89=h( 89) , h90=h( 90)  ,
h91=h( 91) , h92=h( 92) , h93=h( 93) , h94=h( 94) , h95=h( 95) , h96=h( 96) , h97=h( 97) , h98=h( 98) , h99=h( 99) , h100= h( 100) 


highh( int num ) =>
    a = array.from( h1 , h2 , h3 , h4 , h5 , h6 , h7 , h8 , h9 , h10  ,
                     h11 , h12 , h13  , h14 , h15  , h16  , h17  , h18  , h19 , h20   ,
                     h21 , h22 , h23  , h24  , h25  , h26 , h27 , h28  , h29 , h30   ,
                     h31 , h32 , h33 , h34 , h35 , h36 , h37 , h38 , h39 , h40  ,
                     h41 , h42 , h43 , h44 , h45 , h46 , h47 , h48 , h49 , h50  ,
                     h51 , h52 , h53 , h54 , h55 , h56 , h57 , h58 , h59 , h60  ,
                     h61 , h62 , h63 , h64 , h65 , h66 , h67  , h68 , h69 , h70  ,
                     h71 , h72 , h73 , h74 , h75  , h76  , h77 , h78  , h79  , h80  ,
                     h81  , h82 , h83 , h84 , h85 , h86  , h87 , h88 , h89 , h90  ,
                     h91 , h92 , h93 , h94 , h95 , h96 , h97 , h98 , h99 , h100  )




    array.sort(a, order.ascending )
    f = array.get(a , num)
    f








// plot(na)












l(num) =>
    day_change = ta.change(time("D"))
    xh = ( nz(ta.barssince( day_change )) + 1 )
    al = ta.highest(high , (xh))
    bl = ta.lowest(low , (xh))


    var float highh = 100000.0 
    var float loww  = 0.0 


    if ta.change(time("D")) 
        highh := al[1] 
        loww  := bl[1]




    var float x = 0.0 


    if ta.change(loww)
        x += 1 


    if x[1] == 100
        x := 1 




    var float l1 = 0.0 


    if x == num and ta.change(loww)
        l1 := loww 


    if ta.crossunder(low , l1 ) 
        l1 := 0.0 
    l1
















l1 = l(1) , l2 = l(2) , l3 =  l(3) , l4 = l(4) , l5 = l(5) ,l6 = l(6) , l7 = l(7) , l8 = l(8) , l9 = l(9) , l10 = l(10) 
l11 = l( 11) , l12= l( 12) ,l13 =  l(13 ) ,l14= l( 14) , l15=l(15 ) , l16=l(16 ) , l17=l(17 ) ,l18= l(18 ) , l19=l( 19) , l20=l(20 ) 
l21 =  l( 21) ,l22 =  l( 22) ,l23= l(23 ) ,l24= l(24 ) ,l25= l(25 ) , l26=l(26 ) , l27= l( 27) , l28=l(28 ) , l29=l( 29) , l30=l(30 )  
l31=l( 31) ,l32= l( 32) ,l33= l( 33) ,l34= l(34 ) ,l35= l(35 ) ,l36= l(36 ) , l37=l(37 ) , l38=l( 38) , l39=l( 39) , l40=l( 40)  
l41=l( 41) , l42=l( 42) , l43=l( 43) , l44=l(44 ) , l45=l( 45) , l46=l(46 ) , l47=l( 47) , l48=l(48 ) , l49=l(49 ) , l50=l( 50)  
l51 = l( 51) , l52 = l( 52) , l53=l( 53) , l54=l( 54) , l55=l( 55) , l56=l( 56) , l57=l(57 ) , l58=l( 58) , l59=l( 59) , l60=l( 60)  ,
l61= l( 61) , l62=l( 62) , l63=l( 63) , l64=l( 64) , l65=l(65 ) , l66=l( 66) , l67=l(67 ) , l68=l(68 ) , l69=l( 69) , l70=l( 70)  ,
l71=l( 71) , l72=l( 72) , l73=l( 73) , l74=l( 74) , l75=l(75 ) , l76=l(76 ) , l77=l( 77) , l78=l(78)  , l79=l(79)  , l80=l( 80)  ,
l81=l(81 ) , l82=l( 82) , l83=l( 83) , l84=l( 84) , l85=l( 85) , l86=l(86 ) , l87=l( 87) , l88=l( 88) , l89=l( 89) , l90=l( 90)  ,
l91=l( 91) , l92=l( 92) , l93=l( 93) , l94=l( 94) , l95=l( 95) , l96=l( 96) , l97=l( 97) , l98=l( 98) , l99=l( 99) , l100= l( 100) 


loww( int num ) =>
    a = array.from( l1 , l2 , l3 , l4 , l5 , l6 , l7 , l8 , l9 , l10  ,
                     l11 , l12 , l13  , l14 , l15  , l16  , l17  , l18  , l19 , l20   ,
                     l21 , l22 , l23  , l24  , l25  , l26 , l27 , l28  , l29 , l30   ,
                     l31 , l32 , l33 , l34 , l35 , l36 , l37 , l38 , l39 , l40  ,
                     l41 , l42 , l43 , l44 , l45 , l46 , l47 , l48 , l49 , l50  ,
                     l51 , l52 , l53 , l54 , l55 , l56 , l57 , l58 , l59 , l60  ,
                     l61 , l62 , l63 , l64 , l65 , l66 , l67  , l68 , l69 , l70  ,
                     l71 , l72 , l73 , l74 , l75  , l76  , l77 , l78  , l79  , l80  ,
                     l81  , l82 , l83 , l84 , l85 , l86  , l87 , l88 , l89 , l90  ,
                     l91 , l92 , l93 , l94 , l95 , l96 , l97 , l98 , l99 , l100  )




    array.sort(a, order.descending )


    f = array.get(a , num)
    f






ll1 = (  loww(0) )
ll2 = (  loww(1)  )
ll3 = (  loww(2)  )




hh1 = (highh( 0))
hh2 = (highh( 1))
hh3 = (highh( 2))




var float zh1  = 0.0 
var float zh2  = 0.0 
var float zh3  = 0.0 
var float zl1  = 0.0 
var float zl2  = 0.0 
var float zl3  = 0.0 






if ta.change(time("D"))
    zh1 := hh1 
    zh2 := hh2 
    zh3 := hh3 
    zl1 := ll1 
    zl2 := ll2 
    zl3 := ll3  
    




c_pink = color.rgb(255, 82, 246, 80) 
c_lime = color.rgb(88, 255, 82, 80)


c_pink1 = color.rgb(255, 82, 246, 0) 
c_lime1 = color.rgb(88, 255, 82, 0)


hz_highh =  plot( fgh(highh) , color = color.red   , style = plot.style_linebr , linewidth = 1  )
hz_loww  =  plot( fgh(loww)  , color = color.green , style = plot.style_linebr , linewidth = 1  )
hz1      = plot( fgh(zh1)    , color = c_pink1       , style = plot.style_linebr , linewidth = 1  )
hz2      = plot( fgh(zh2)    , color = c_pink1       , style = plot.style_linebr , linewidth = 1  )
hz3      = plot( fgh(zh3)    , color = c_pink1       , style = plot.style_linebr , linewidth = 1  )
lz1      = plot( fgh(zl1)    , color = c_lime1       , style = plot.style_linebr , linewidth = 1  )
lz2      = plot( fgh(zl2)    , color = c_lime1       , style = plot.style_linebr , linewidth = 1  )
lz3      = plot( fgh(zl3)    , color = c_lime1       , style = plot.style_linebr , linewidth = 1  )








// previous day high low and close 


fynction_line( (highh) , color.red   , 1 )
fynction_line( (loww)  , color.green , 1 )
fynction_line( (cxv)   , color.white , 1 )




// previous day unbreachable high and low 


fynction_line( zh1   , c_pink1 , 1 )
fynction_line( zh2   , c_pink1 , 1 )
fynction_line( zh3   , c_pink1 , 1 )
fynction_line( zl1   , c_lime1 , 1 )
fynction_line( zl2   , c_lime1 , 1 )
fynction_line( zl3   , c_lime1 , 1 )














/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////














/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////












// newLine_highh = line.new(bar_index - 1 , (highh) , bar_index + 1, (highh), extend = extend.right, color = color.red , width = 2)
// pd_highh = line.new(bar_index - 1 , (al) , bar_index + 1, (al), extend = extend.right, color = color.red , width = 2)


// newLine1 = line.new(bar_index - 1 , (zh1) , bar_index + 1, (zh1), extend = extend.right, color = c_pink1 , width = 2)
// newLine2 = line.new(bar_index - 1 , (zh2) , bar_index + 1, (zh2), extend = extend.right, color =c_pink1 , width = 2)
// newLine3 = line.new(bar_index - 1 , (zh3) , bar_index + 1, (zh3), extend = extend.right, color = c_pink1 , width = 2)


// newLine4 = line.new(bar_index - 1 , (zl1 ) , bar_index + 1, (zl1 ), extend = extend.right, color = c_lime1 , width = 2)
// newLine5 = line.new(bar_index - 1 , (zl2 ) , bar_index + 1, (zl2 ), extend = extend.right, color = c_lime1 , width = 2)
// newLine6 = line.new(bar_index - 1 , (zl3 ) , bar_index + 1, (zl3 ), extend = extend.right, color = c_lime1 , width = 2)


// newLine_loww = line.new(bar_index - 1 , ( loww ) , bar_index + 1, ( loww ), extend = extend.right, color = color.green , width = 2)
// pd_loww = line.new(bar_index - 1 , ( bl ) , bar_index + 1, ( bl ), extend = extend.right, color = color.green , width = 2)


// line.delete(newLine1[1])
// line.delete(newLine2[1])
// line.delete(newLine3[1])
// line.delete(newLine4[1])
// line.delete(newLine5[1])
// line.delete(newLine6[1])
// line.delete(newLine_highh[1])
// line.delete(newLine_loww[1])
// line.delete(pd_highh[1])
// line.delete(pd_loww[1])






















highh1 = zh1
highh2 = zh2
highh3 = zh3


loww1 = zl1
loww2 = zl2
loww3 = zl3




label_( (highh1) , color.rgb(255, 82, 246, 0)  , "high 1"   )
label_( (highh2) , color.rgb(255, 82, 246, 0)  , "high 2"   )
label_( (highh3) , color.rgb(255, 82, 246, 0)  , "high 3"   )
label_( (loww1)  , color.rgb(88, 255, 82, 0)   , "low 1"    )
label_( (loww2)  , color.rgb(88, 255, 82, 0)   , "low 2"    )
label_( (loww3)  , color.rgb(88, 255, 82, 0)   , "low 3"    )
















ohlc_tip = "This will show the OHLC for selected range. If you select D then todays high-lows and previous days high-lows both will show up, same applies for Weekly and other timeframe's as well."
centrepptip = "This will show Centre Pivot Point lines for Daily, Weekly and Monthly timeframes atonce."
cpp = input.bool(title="Enable/Disable Centre PP", defval=false, tooltip = centrepptip)
pivotpointsohlc = input.string(title="Timeframe", defval="D", options=["D", "W", "M", "3M", "6M", "12M"], inline="SPP", group="Fibonacci Pivot Points & OHLC")
fbpp = input.bool(title="Fibonacci", defval=true, inline="SPP", group="Fibonacci Pivot Points & OHLC")
ohlc = input.bool(title="OHLC", defval=false, inline="SPP", group="Fibonacci Pivot Points & OHLC")
num = input(3, "Previous Days High/Low", group="Fibonacci Pivot Points & OHLC")
rng = input(20.0, "zone range for Support/Resistance")


//OHLC for different timeframes
[dOpen, dHigh, dLow, pHigh, pLow, pClose] = request.security(syminfo.tickerid, pivotpointsohlc, [open, high, low, ta.highest(high[1], num), ta.lowest(low[1], num) , close[1]], barmerge.gaps_off, barmerge.lookahead_on)
[High, Low, Close] = request.security(syminfo.tickerid, "D", [high[1], low[1], close[1]], barmerge.gaps_off, barmerge.lookahead_on)
//Calculation


//CPP Calculation
pRange = highh - loww
dcpp = ( highh + loww + closee ) / 3.0
//Fibonacci
SPP = (highh + loww + nonRepaintingClose) / 3.0
PR1 = SPP + pRange * 0.382
PR2 = SPP + pRange * 0.618
PR3 = SPP + pRange * 1.000
PS1 = SPP - pRange * 0.382
PS2 = SPP - pRange * 0.618
PS3 = SPP - pRange * 1.000






//Fibonacci Plots






r3 = plot(   fgh(PR3) ,  color=color.new(color.red,0) , style = plot.style_linebr , title="Fibonacci R3", linewidth=line_ )


r2 = plot(   fgh(PR2) , color=color.new(color.red,0) , style = plot.style_linebr , title="Fibonacci R2", linewidth=line_ )


r1 = plot(   fgh(PR1) , color=color.new(color.red,0) , style = plot.style_linebr , title="Fibonacci R1", linewidth= line_ )


p_p = plot(  fgh (SPP) , color=color.new(color.blue,0) , style = plot.style_linebr , title="Fibonacci PP", linewidth= line_ )


s1 = plot(   fgh(PS1) , color=color.new(color.green,0) , style = plot.style_linebr , title="Fibonacci S1", linewidth= line_ )


s2 = plot(   fgh(PS2) , color=color.new(color.green,0) , style = plot.style_linebr , title="Fibonacci S2", linewidth= line_ )


s3 = plot(   fgh(PS3) , color=#4caf50 , style = plot.style_linebr , title="Fibonacci S3", linewidth= line_ )




label_( PR3 , color.new(color.red,0) , "PR3"   )
label_( PR2 , color.new(color.red,0) , "PR2"   )
label_( PR1 , color.new(color.red,0) , "PR1"   )
label_( SPP , color.new(color.blue,0) , "SPP"  )
label_( PS1 , color.new(color.green,0) , "PS1" )
label_( PS2 , color.new(color.green,0) , "PS2" )
label_( PS3 , color.new(color.green,0) , "PS3" )




// newLine7 = line.new(bar_index - xh1 , fgh(PR3) , bar_index + 1, fgh(PR3), extend = extend.right, color = color.new(color.red,0) , width = 1)
// newLine8 = line.new(bar_index - xh1, fgh(PR2) , bar_index + 1, fgh(PR2), extend = extend.right, color = color.new(color.red,0) , width = 1)
// newLine9 = line.new(bar_index - xh1 , fgh(PR1) , bar_index + 1, fgh(PR1), extend = extend.right, color = color.new(color.red,0) , width = 1)


// newLine10 = line.new(bar_index - xh1 , fgh(SPP) , bar_index + 1, fgh(SPP), extend = extend.right, color = color.new(color.blue,0) , width = 1)
// newLine14 = line.new(bar_index - xh1 , fgh(nonRepaintingClose) , bar_index + 1, fgh(nonRepaintingClose), extend = extend.right, color = color.new(color.white,0) , width = 1)


// newLine11 = line.new(bar_index - xh1 , fgh(PS1) , bar_index + 1, fgh(PS1), extend = extend.right, color = color.new(color.green,0) , width = 1)
// newLine12 = line.new(bar_index - xh1 , fgh(PS2) , bar_index + 1, fgh(PS2), extend = extend.right, color = color.new(color.green,0) , width = 1)
// newLine13 = line.new(bar_index - xh1 , fgh(PS3) , bar_index + 1, fgh(PS3), extend = extend.right, color = color.new(color.green,0) , width = 1)




// line.delete(newLine11 [1])
// line.delete(newLine12 [1])
// line.delete(newLine13 [1])
// line.delete(newLine10 [1])
// line.delete(newLine9 [1])
// line.delete(newLine8 [1])
// line.delete(newLine7 [1])
// line.delete(newLine14 [1])








fynction_line( PR3 , color.new(color.red,0)   , 1 )
fynction_line( PR2 , color.new(color.red,0)   , 1 )
fynction_line( PR1 , color.new(color.red,0)   , 1 )
fynction_line( SPP , color.new(color.blue,0)  , 1 )
fynction_line( PS1 , color.new(color.green,0) , 1 )
fynction_line( PS2 , color.new(color.green,0) , 1 )
fynction_line( PS3 , color.new(color.green,0) , 1 )














/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////














var hi = 0.0
var lo = 0.0
var op = 0.0


if dayofmonth!=dayofmonth[1]
    hi:=high
    lo:=low
    op := open
else
    hi:=math.max(hi, high)
    lo:=math.min(lo, low)












vS1 = PS1
vS2 = PS2
vS3 = PS3
vPP = SPP








label_2( series_ , color_ ,  se ) => 
    if timeframe.change("D")  and  on_off_newline == true 
        WlabelWL1 = label.new( bnm , series_ , ''+se+'', xloc.bar_index , yloc.price , color = color_ , style=label.style_label_center , textcolor= color.black , size=size.normal, textalign=text.align_center )
        label.delete(WlabelWL1[1])






label_2( nonRepaintingClose , color.white , "prv close"  )




var float days_open = 100000.0 




if day_change
    days_open := open 


// bnbm = input.int( 10 , "tell me the candal")  
// 


a = number_of_bars - 1 //  timeframe.period == "1" ? 374 :
    //  timeframe.period == "3" ? 124 :
    //  timeframe.period == "5" ? 74 : 
    //  timeframe.period == "10" ? 37 : 
    //  timeframe.period == "15" ? 24 : 
    //  timeframe.period == "60" ? 6 : 
    //  timeframe.period == "D" ? 0 : na




bnbm = a 


making_zone2( bnbm , vS1 , vr ) => 


    bar = ta.barssince((ta.change(time("D"))))


    var float mark_s1 = 0.0 


    if (days_open > vS1 ) and day_change
        mark_s1 := vS1


    if (vr == 1 ? ta.crossunder(low , mark_s1 ) : ta.crossunder(low , mark_s1[1] ) ) or bar ==  bnbm            
        mark_s1 := 0.0 


    var float highh_vs1 = 0.0 
    var float loww_vs1 = 0.0 


    if vr == 1 ? ta.crossunder(low , mark_s1[1] )  : ta.crossunder(low , mark_s1 ) 
        highh_vs1 := vS1 
        loww_vs1  := low 


    if low < loww_vs1 and high > highh_vs1
        loww_vs1 := low 


    if  bar ==  bnbm  
        highh_vs1 := na 
        loww_vs1  := na


    




    [ highh_vs1 , loww_vs1   ]




asd = 1
asm = 2


///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////




[ highh_vs0c_ , loww_vs0c_  ] = making_zone2( a , nonRepaintingClose , asd )


[ highh_vs0_ , loww_vs0_     ] = making_zone2( a , vPP  , asd  )
[ highh_vs1_ , loww_vs1_     ] = making_zone2( a , vS1  , asd  )
[ highh_vs2_ , loww_vs2_    ] = making_zone2( a , vS2  , asd  )
[ highh_vs3_ , loww_vs3_    ] = making_zone2( a , vS3  , asd  )


[ highh_vs4_ , loww_vs4_     ] = making_zone2( a , loww1 , asd  )
[ highh_vs5_ , loww_vs5_     ] = making_zone2( a , loww2 , asd  )
[ highh_vs6_ , loww_vs6_     ] = making_zone2( a , loww3 , asd  )




///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////


[ highh_vs0c_z , loww_vs0c_z  ] = making_zone2( a , nonRepaintingClose , asm )


[ highh_vs0_z , loww_vs0_z    ] = making_zone2( a , vPP  , asm  )
[ highh_vs1_z , loww_vs1_z     ] = making_zone2( a , vS1  , asm  )
[ highh_vs2_z , loww_vs2_z    ] = making_zone2( a , vS2  , asm  )
[ highh_vs3_z , loww_vs3_z     ] = making_zone2( a , vS3  , asm  )


[ highh_vs4_z , loww_vs4_z     ] = making_zone2( a , loww1 , asm  )
[ highh_vs5_z , loww_vs5_z    ] = making_zone2( a , loww2 , asm  )
[ highh_vs6_z , loww_vs6_z     ] = making_zone2( a , loww3 , asm  )


///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////


unuseful = true 
p1_ = plot( unuseful and hide_z  ? na(loww_vs0_)  ? fgh(loww_vs0_z)  : fgh(loww_vs0_)   : na  , color = color.blue             , style = plot.style_linebr  , linewidth = 1  )
p_ = plot( unuseful and hide_z  ? na(loww_vs0_)  ? fgh(loww_vs0_z)  : fgh(highh_vs0_)   : na  , color = color.blue             , style = plot.style_linebr  , linewidth = 1  )






p2_ = plot( unuseful and hide_z  ? na(loww_vs0c_) ? fgh( loww_vs0c_z ) : fgh( loww_vs0c_)  : na  , color = color.white            , style = plot.style_linebr  , linewidth = 1  )
p3_ = plot( unuseful and hide_z  ? na(loww_vs1_)  ? fgh( loww_vs1_z  ) : fgh( loww_vs1_ )  : na  , color = color.green            , style = plot.style_linebr  , linewidth = 1  )
p4_ = plot( unuseful and hide_z  ? na(loww_vs2_)  ? fgh( loww_vs2_z  ) : fgh( loww_vs2_ )  : na  , color = color.green            , style = plot.style_linebr  , linewidth = 1  )
p5_ = plot( unuseful and hide_z  ? na(loww_vs3_)  ? fgh( loww_vs3_z  ) : fgh( loww_vs3_ )  : na  , color = color.green            , style = plot.style_linebr  , linewidth = 1  )
p6_ = plot( unuseful and hide_z  ? na(loww_vs4_)  ? fgh( loww_vs4_z  ) : fgh( loww_vs4_ )  : na  , color = color.green            , style = plot.style_linebr  , linewidth = 1  )
p7_ = plot( unuseful and hide_z  ? na(loww_vs5_)  ? fgh( loww_vs5_z  ) : fgh( loww_vs5_ )  : na  , color = color.green            , style = plot.style_linebr  , linewidth = 1  )
p8_ = plot( unuseful and hide_z  ? na(loww_vs6_)  ? fgh( loww_vs6_z  ) : fgh( loww_vs6_ )  : na  , color = color.green            , style = plot.style_linebr  , linewidth = 1  )


// p6 = plot( hide_z ? loww : na , color = color.green , title = "previous day close" , linewidth =  2 )






// plot( loww_vs1_ )
// plot( loww_vs2_ )
// plot( loww_vs3_ )
// plot( loww_vs4_ )
// plot( loww_vs5_ )
// plot( loww_vs6_ )




























fill(p_p,p1_, color = color.new(#0011ff, 80))


fill(s1,p3_, color = color.new(color.green, 80))
fill(s2,p4_, color = color.new(color.green, 80))
fill(s3,p5_, color = color.new(color.green, 80))


fill(c1,p2_, color = color.new(#ffffff, 80))


fill(lz1,p6_, color = c_lime)
fill(lz2,p7_, color = c_lime)
fill(lz3,p8_, color = c_lime)








box_new_s( cond_ , highh , loww , col   ) => 


    var float lowx1 = 0.0 


    if ta.change( cond_ ) 
        lowx1 := cond_


    bar_since_making_zone = bar_index - ta.barssince( not na(cond_) and na(cond_[1]) ) 


    topLeft = chart.point.now( highh )


    bottomRight = chart.point.from_index(bar_index + ( number_of_bars - (xh1  ) ) , loww )


    if ( not na(cond_) and na(cond_[1])  ) or ta.change( cond_ )  and  on_off_newline == true 
        agh = box.new(topLeft, bottomRight,  color.new( col, 80 ) , 2, bgcolor = color.new( col , 80 )  )


        box.delete(agh[1])
        if timeframe.change("D") 
            box.delete(agh[1])


        if ta.change( cond_ ) 
            box.set_left(agh ,  bar_since_making_zone )




















box_new_s( loww_vs1_ , highh_vs1_  , loww_vs1_ , color.green ) 
box_new_s( loww_vs2_ , highh_vs2_  , loww_vs2_ , color.green ) 
box_new_s( loww_vs3_ , highh_vs3_  , loww_vs3_ , color.green ) 
box_new_s( loww_vs4_ , highh_vs4_  , loww_vs4_ , color.green ) 
box_new_s( loww_vs5_ , highh_vs5_  , loww_vs5_ , color.green ) 
box_new_s( loww_vs6_ , highh_vs6_  , loww_vs6_ , color.green ) 






box_new_s( loww_vs0_  , highh_vs0_   , loww_vs0_  , color.blue ) 
box_new_s( loww_vs0c_ , highh_vs0c_  , loww_vs0c_ , color.white )


/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////


box_new_s( loww_vs1_z , highh_vs1_z  , loww_vs1_z , color.green ) 
box_new_s( loww_vs2_z , highh_vs2_z  , loww_vs2_z , color.green ) 
box_new_s( loww_vs3_z , highh_vs3_z  , loww_vs3_z , color.green ) 
box_new_s( loww_vs4_z , highh_vs4_z  , loww_vs4_z , color.green ) 
box_new_s( loww_vs5_z , highh_vs5_z  , loww_vs5_z , color.green ) 
box_new_s( loww_vs6_z , highh_vs6_z  , loww_vs6_z , color.green ) 






box_new_s( loww_vs0_z  , highh_vs0_z   , loww_vs0_z  , color.blue ) 
box_new_s( loww_vs0c_z , highh_vs0c_z  , loww_vs0c_z , color.white ) 




/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
































making_zone_s2( bnbm , vR1 , vr  ) =>




    bar = ta.barssince( (ta.change(time("D"))))


    var float mark_s1 = 100000.0 


    if  (days_open < vR1  )  and  day_change
        mark_s1 := vR1


    if (vr == 1 ? ta.crossover(high , mark_s1 ) :  ta.crossover(high , mark_s1[1] ) ) or bar ==  bnbm            
        mark_s1 :=100000.0 


    var float highh_vr1 = 100000.0 
    var float loww_vr1 = 100000.0 


    if vr == 1 ? ta.crossover(high , mark_s1[1] ) : ta.crossover(high , mark_s1 )
        highh_vr1 := high 
        loww_vr1  := vR1 


    if low < loww_vr1 and high > highh_vr1 
        highh_vr1 := high 


    if  bar ==  bnbm 
        highh_vr1 := na 
        loww_vr1  := na 






    [ highh_vr1 , loww_vr1  ]


vR1 = PR1
vR2 = PR2
vR3 = PR3


/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////


[ highh_vr0c_, loww_vr0c_   ] = making_zone_s2( bnbm , nonRepaintingClose , asd  )


[ highh_vr0_ , loww_vr0_   ] = making_zone_s2( bnbm , vPP   , asd  )
[ highh_vr1_ , loww_vr1_      ] = making_zone_s2( bnbm , vR1   , asd   )
[ highh_vr2_ , loww_vr2_    ] = making_zone_s2( bnbm , vR2   , asd   )
[ highh_vr3_ , loww_vr3_     ] = making_zone_s2( bnbm , vR3   , asd   )


[ highh_vr4_ , loww_vr4_   ] = making_zone_s2( bnbm , highh1 , asd   )
[ highh_vr5_ , loww_vr5_    ] = making_zone_s2( bnbm , highh2 , asd   )
[ highh_vr6_ , loww_vr6_  ] = making_zone_s2( bnbm , highh3 , asd   )




/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////




[ highh_vr0c_z, loww_vr0c_z    ] = making_zone_s2( bnbm , nonRepaintingClose , asm  )


[ highh_vr0_z , loww_vr0_z    ] = making_zone_s2( bnbm , vPP   , asm  )
[ highh_vr1_z , loww_vr1_z    ] = making_zone_s2( bnbm , vR1   , asm   )
[ highh_vr2_z , loww_vr2_z    ] = making_zone_s2( bnbm , vR2   , asm   )
[ highh_vr3_z , loww_vr3_z     ] = making_zone_s2( bnbm , vR3   , asm   )


[ highh_vr4_z , loww_vr4_z    ] = making_zone_s2( bnbm , highh1 , asm   )
[ highh_vr5_z , loww_vr5_z     ] = making_zone_s2( bnbm , highh2 , asm   )
[ highh_vr6_z , loww_vr6_z     ] = making_zone_s2( bnbm , highh3 , asm   )




/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////


// bgcolor(up1 ? color.red : na )


r1_ = plot( unuseful and hide_z  ? na(highh_vr1_)  ? fgh( highh_vr1_z )  : fgh(highh_vr1_ ) : na  , color = color.red , style = plot.style_linebr  , linewidth = 1  )


r2_ = plot( unuseful and hide_z  ? na(highh_vr2_)  ? fgh( highh_vr2_z )  : fgh(highh_vr2_ ) : na  , color = color.red , style = plot.style_linebr , linewidth = 1   )


r3_ = plot( unuseful and hide_z  ? na(highh_vr3_)  ? fgh( highh_vr3_z )  : fgh(highh_vr3_ ) : na  , color = color.red , style = plot.style_linebr , linewidth = 1   )


r4_ = plot( unuseful and hide_z  ? na(highh_vr0_)  ? fgh( highh_vr0_z )  : fgh(highh_vr0_ ) : na  , color = color.blue , style = plot.style_linebr , linewidth = 1   )


r5_ = plot( unuseful and hide_z  ? na(highh_vr0c_) ? fgh( highh_vr0c_z ) : fgh(highh_vr0c_) : na  , color = color.white , style = plot.style_linebr , linewidth = 1   )


r6_ = plot( unuseful and hide_z  ? na(highh_vr4_)  ? fgh( highh_vr4_z )  : fgh(highh_vr4_ ) : na  , color = color.red , style = plot.style_linebr , linewidth = 1  )


r7_ = plot( unuseful and hide_z  ? na(highh_vr5_)  ? fgh( highh_vr5_z )  : fgh(highh_vr5_ ) : na  , color = color.red , style = plot.style_linebr , linewidth = 1  )


r8_ = plot( unuseful and hide_z  ? na(highh_vr6_)  ? fgh( highh_vr6_z )  : fgh(highh_vr6_ ) : na  , color = color.red , style = plot.style_linebr , linewidth = 1  )


// r6  = plot( hide_z ? highh : na , color = color.red , title = "previous day high"  , linewidth = 2   )


/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////


fill(p_p,r4_, color = color.new(#0011ff, 80))
fill(c1,r5_ , color = color.new(#ffffff, 80))


fill(r1,r1_ , color = color.new(color.red, 80))
fill(r2,r2_ , color = color.new(color.red, 80))
fill(r3,r3_ , color = color.new(color.red, 80))


fill(hz1,r6_ , color = c_pink)
fill(hz2,r7_ , color = c_pink)
fill(hz3,r8_ , color = c_pink)














box_new_r( cond_ , highh , loww , col   ) => 


    var float highx1 = 100000.0 


    if ta.change( cond_ ) 
        highx1 := cond_


    bar_since_making_zone = bar_index - ta.barssince( not na(cond_) and na(cond_[1]) ) 


    topLeft =  chart.point.from_index(bar_index  , highh )


    bottomRight =  chart.point.from_index(bar_index + ( number_of_bars - (xh1  )  ) , loww )


    if ( not na(cond_) and na(cond_[1])  ) or ta.change( cond_ )  and  on_off_newline == true 
        agh = box.new(topLeft, bottomRight,  color.new( col, 80 ) , 2, bgcolor = color.new( col , 80 )  )


        box.delete(agh[1])


        box.set_left(agh ,  bar_since_making_zone )






box_new_r( highh_vr0c_ , highh_vr0c_ , loww_vr0c_ , color.white   )
box_new_r( highh_vr0_ , highh_vr0_  , loww_vr0_  , color.blue   )


box_new_r( highh_vr1_ , highh_vr1_  , loww_vr1_  , color.red   )
box_new_r( highh_vr2_ , highh_vr2_  , loww_vr2_  , color.red   )
box_new_r( highh_vr3_ , highh_vr3_  , loww_vr3_  , color.red   )


box_new_r( highh_vr4_ , highh_vr4_  , loww_vr4_  , c_pink1   )
box_new_r( highh_vr5_ , highh_vr5_  , loww_vr5_  , c_pink1   )
box_new_r( highh_vr6_ , highh_vr6_  , loww_vr6_  , c_pink1   )
















[alx , blx , highhx , lowwx , closeex ] = moksh(ires) 




a_allBoxes = box.all
if array.size(a_allBoxes) > 0 and (timeframe.change("D") or on_off_newline == false )
    for i = 0 to array.size(a_allBoxes) - 1
        box.delete(array.get(a_allBoxes, i))






pRangex = alx - blx 
dcppx   = (alx  + blx  + close  ) / 3.0
//Fibonacci
SPPx = (alx + blx + close) / 3.0
PR1x = SPPx + pRangex * 0.382
PR2x = SPPx + pRangex * 0.618
PR3x = SPPx + pRangex * 1.000
PS1x = SPPx - pRangex * 0.382
PS2x = SPPx - pRangex * 0.618
PS3x = SPPx - pRangex * 1.000








if number_of_bars - 2  == (xh1  ) and next_day_levels 
    newLine1 = line.new(bar_index + 2 , al    , bar_index + number_of_bars , al    , extend = extend.none , color = color.rgb(255, 0, 0)   , width = 1 )
    newLine2 = line.new(bar_index + 2 , bl    , bar_index + number_of_bars , bl    , extend = extend.none , color = color.rgb(0, 255, 8) , width = 1 )
    newLine3 = line.new(bar_index + 2 , close , bar_index + number_of_bars , close , extend = extend.none , color = color.white , width = 1 )


    newLine4  = line.new(bar_index + 2 , SPPx , bar_index + number_of_bars , SPPx , extend = extend.none , color = color.blue , width = 1 )
    newLine5  = line.new(bar_index + 2 , PR1x , bar_index + number_of_bars , PR1x , extend = extend.none , color = color.red , width = 1 )
    newLine6  = line.new(bar_index + 2 , PR2x , bar_index + number_of_bars , PR2x , extend = extend.none , color = color.red , width = 1 )
    newLine7  = line.new(bar_index + 2 , PR3x , bar_index + number_of_bars , PR3x , extend = extend.none , color = color.red , width = 1 )
    newLine8  = line.new(bar_index + 2 , PS1x , bar_index + number_of_bars , PS1x , extend = extend.none , color = color.green , width = 1 )
    newLine9  = line.new(bar_index + 2 , PS2x , bar_index + number_of_bars , PS2x , extend = extend.none , color = color.green , width = 1 )
    newLine10 = line.new(bar_index + 2 , PS3x , bar_index + number_of_bars , PS3x , extend = extend.none , color = color.green , width = 1 )




    line.delete(newLine1[1])
    line.delete(newLine2[1])
    line.delete(newLine3[1])
    line.delete(newLine4[1])
    line.delete(newLine5[1])
    line.delete(newLine6[1])
    line.delete(newLine7[1])
    line.delete(newLine8[1])
    line.delete(newLine9[1])
    line.delete(newLine10[1])