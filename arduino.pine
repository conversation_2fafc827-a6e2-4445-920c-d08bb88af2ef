//@version=5
indicator("Auto Levels with Alerts", overlay=true)

// ---------------- Inputs ---------------- //
levels_to_show = input.int(3, "Number of Levels", minval=1)
only_today = input.bool(false, "Only Today's Levels")
alert_enabled = input.bool(true, "Enable Alerts")

// ---------------- Helper Functions ---------------- //
// Get sorted value from array
get_sorted(arr, order, idx) =>
    if array.size(arr) > idx
        sorted = array.copy(arr)
        array.sort(sorted, order)
        array.get(sorted, idx)
    else
        na

// Price touch detection
priceTouch(level) =>
    ta.crossover(close, level) or ta.crossunder(close, level)

// Color gradient
getColor(baseColor, idx, total) =>
    color.new(baseColor, math.round(50 * idx / total))

// Label function
label_(level, lvlColor, txt) =>
    label.new(bar_index, level, txt, yloc=yloc.price, style=label.style_label_up, color=lvlColor, textcolor=color.white, size=size.tiny)

// Alert message formatter
getAlertMessage(type, level) =>
    emoji = type == "High" ? "📈" : "📉"
    str.format("{0} {1} touched at {2} on {3}", emoji, type, str.tostring(level), str.tostring(time, "dd-MMM HH:mm"))

// ---------------- Arrays to track highs/lows ---------------- //
var float[] highs = array.new_float()
var float[] lows = array.new_float()
var int[] bars_recorded = array.new_int()

// ---------------- Capture Highs & Lows ---------------- //
if not only_today or dayofmonth(time) == dayofmonth(time)
    array.push(highs, high)
    array.push(lows, low)
    array.push(bars_recorded, dayofmonth(time))

// ---------------- Plot Levels & Alerts ---------------- //
for i = 0 to levels_to_show - 1
    hh = get_sorted(highs, order.ascending, i)
    ll = get_sorted(lows,  order.descending, i)

    highColor = getColor(color.red, i, levels_to_show)
    lowColor  = getColor(color.green, i, levels_to_show)

    if not na(hh)
        plot(hh, "High" + str.tostring(i+1), color=highColor, style=plot.style_linebr)
        label_(hh, highColor, "H" + str.tostring(i+1))
        if alert_enabled
            alertcondition(priceTouch(hh), 
                           title="Price touched High " + str.tostring(i+1), 
                           message=getAlertMessage("High", hh))

    if not na(ll)
        plot(ll, "Low" + str.tostring(i+1), color=lowColor, style=plot.style_linebr)
        label_(ll, lowColor, "L" + str.tostring(i+1))
        if alert_enabled
            alertcondition(priceTouch(ll), 
                           title="Price touched Low " + str.tostring(i+1), 
                           message=getAlertMessage("Low", ll))
