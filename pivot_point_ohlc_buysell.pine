//@version=5
indicator("Pivot Point OHLC Buy/Sell", overlay=true, max_bars_back=500, max_boxes_count=500, max_labels_count=500, max_lines_count=500)

// ==================== INPUTS ====================
// Main Controls
show_historical = input.bool(true, "Show Historical Lines/Zones", group="Display Options")
show_current = input.bool(true, "Show Current Day Lines/Zones", group="Display Options")
show_next_day = input.bool(false, "Show Next Day Levels", group="Display Options")

// Pivot Settings
pivot_timeframe = input.string("Day", "Pivot Timeframe", options=["Day", "Week", "Month", "Quarter", "Year"], group="Pivot Settings")
zone_range = input.float(0.1, "Zone Range (%)", minval=0.01, maxval=5.0, step=0.01, group="Pivot Settings")

// Pivot Types
enable_cpp = input.bool(true, "Enable Center Pivot Point (CPP)", group="Pivot Types")
enable_fibonacci = input.bool(true, "Enable Fibonacci Pivots", group="Pivot Types")
enable_standard = input.bool(true, "Enable Standard Pivots", group="Pivot Types")

// Visual Settings
line_width = input.int(1, "Line Width", minval=1, maxval=5, group="Visual Settings")
box_transparency = input.int(85, "Box Transparency", minval=0, maxval=100, group="Visual Settings")
show_labels = input.bool(true, "Show Labels", group="Visual Settings")

// Trading Signals
enable_signals = input.bool(true, "Enable Buy/Sell Signals", group="Trading Signals")
confirmation_bars = input.int(1, "Confirmation Bars", minval=1, maxval=5, group="Trading Signals")
enable_alerts = input.bool(false, "Enable Alerts", group="Trading Signals")

// ==================== TIMEFRAME CONVERSION ====================
get_timeframe() =>
    switch pivot_timeframe
        "Day" => "1D"
        "Week" => "1W"
        "Month" => "1M"
        "Quarter" => "3M"
        "Year" => "12M"
        => "1D"

// ==================== PIVOT CALCULATIONS ====================
// Get previous OHLC data
[prev_open, prev_high, prev_low, prev_close] = request.security(syminfo.tickerid, get_timeframe(), 
    [open[1], high[1], low[1], close[1]], barmerge.gaps_off, barmerge.lookahead_on)

// Center Pivot Point (CPP)
cpp = (prev_high + prev_low + prev_close) / 3

// Standard Pivot Levels
pr1_std = 2 * cpp - prev_low
pr2_std = cpp + (prev_high - prev_low)
pr3_std = prev_high + 2 * (cpp - prev_low)

ps1_std = 2 * cpp - prev_high
ps2_std = cpp - (prev_high - prev_low)
ps3_std = prev_low - 2 * (prev_high - cpp)

// Fibonacci Pivot Levels
range_hl = prev_high - prev_low
pr1_fib = cpp + range_hl * 0.382
pr2_fib = cpp + range_hl * 0.618
pr3_fib = cpp + range_hl * 1.000

ps1_fib = cpp - range_hl * 0.382
ps2_fib = cpp - range_hl * 0.618
ps3_fib = cpp - range_hl * 1.000

// ==================== HELPER FUNCTIONS ====================
// Check if new timeframe period started
is_new_period() =>
    ta.change(time(get_timeframe())) != 0

// Create zone boundaries
get_zone_range(price) =>
    zone_size = price * zone_range / 100
    [price - zone_size, price + zone_size]

// Color definitions
color_cpp = color.new(color.blue, 0)
color_resistance = color.new(color.red, 0)
color_support = color.new(color.green, 0)
color_fib_resistance = color.new(color.orange, 0)
color_fib_support = color.new(color.purple, 0)

// ==================== PLOTTING FUNCTIONS ====================
// Plot line with optional historical display
plot_level(price, col, title_str, show_hist) =>
    display_condition = show_current or (show_hist and show_historical)
    plot(display_condition ? price : na, title=title_str, color=col, linewidth=line_width, style=plot.style_line)

// Create zone box
create_zone_box(price, col, label_text) =>
    if show_current and not na(price)
        [zone_low, zone_high] = get_zone_range(price)
        box_color = color.new(col, box_transparency)
        
        // Create box for current session
        if barstate.islast
            current_box = box.new(bar_index - 50, zone_low, bar_index + 50, zone_high, 
                border_color=col, bgcolor=box_color, border_width=1)
        
        // Add label if enabled
        if show_labels and barstate.islast
            label.new(bar_index, price, label_text, color=col, style=label.style_label_left, 
                textcolor=color.white, size=size.small)

// ==================== MAIN PLOTTING ====================
// Plot CPP
if enable_cpp
    plot_level(cpp, color_cpp, "CPP", true)
    create_zone_box(cpp, color_cpp, "CPP")

// Plot Standard Pivots
if enable_standard
    // Resistance levels
    plot_level(pr1_std, color_resistance, "PR1", true)
    plot_level(pr2_std, color_resistance, "PR2", true)
    plot_level(pr3_std, color_resistance, "PR3", true)
    
    // Support levels
    plot_level(ps1_std, color_support, "PS1", true)
    plot_level(ps2_std, color_support, "PS2", true)
    plot_level(ps3_std, color_support, "PS3", true)
    
    // Create zones
    create_zone_box(pr1_std, color_resistance, "PR1")
    create_zone_box(pr2_std, color_resistance, "PR2")
    create_zone_box(pr3_std, color_resistance, "PR3")
    create_zone_box(ps1_std, color_support, "PS1")
    create_zone_box(ps2_std, color_support, "PS2")
    create_zone_box(ps3_std, color_support, "PS3")

// Plot Fibonacci Pivots
if enable_fibonacci
    // Fibonacci resistance levels
    plot_level(pr1_fib, color_fib_resistance, "FPR1", true)
    plot_level(pr2_fib, color_fib_resistance, "FPR2", true)
    plot_level(pr3_fib, color_fib_resistance, "FPR3", true)
    
    // Fibonacci support levels
    plot_level(ps1_fib, color_fib_support, "FPS1", true)
    plot_level(ps2_fib, color_fib_support, "FPS2", true)
    plot_level(ps3_fib, color_fib_support, "FPS3", true)
    
    // Create Fibonacci zones
    create_zone_box(pr1_fib, color_fib_resistance, "FPR1")
    create_zone_box(pr2_fib, color_fib_resistance, "FPR2")
    create_zone_box(pr3_fib, color_fib_resistance, "FPR3")
    create_zone_box(ps1_fib, color_fib_support, "FPS1")
    create_zone_box(ps2_fib, color_fib_support, "FPS2")
    create_zone_box(ps3_fib, color_fib_support, "FPS3")

// Plot Previous OHLC levels
plot_level(prev_high, color.new(color.red, 30), "Prev High", show_historical)
plot_level(prev_low, color.new(color.green, 30), "Prev Low", show_historical)
plot_level(prev_close, color.new(color.gray, 30), "Prev Close", show_historical)

// ==================== BUY/SELL SIGNAL LOGIC ====================
// Signal detection functions
is_bullish_confirmation() =>
    close > open and close[1] > open[1]

is_bearish_confirmation() =>
    close < open and close[1] < open[1]

// Buy signals - price breaks above resistance with confirmation
buy_signal_pr1 = enable_signals and enable_standard and ta.crossover(close, pr1_std) and is_bullish_confirmation()
buy_signal_pr2 = enable_signals and enable_standard and ta.crossover(close, pr2_std) and is_bullish_confirmation()
buy_signal_fpr1 = enable_signals and enable_fibonacci and ta.crossover(close, pr1_fib) and is_bullish_confirmation()
buy_signal_fpr2 = enable_signals and enable_fibonacci and ta.crossover(close, pr2_fib) and is_bullish_confirmation()

// Sell signals - price breaks below support with confirmation
sell_signal_ps1 = enable_signals and enable_standard and ta.crossunder(close, ps1_std) and is_bearish_confirmation()
sell_signal_ps2 = enable_signals and enable_standard and ta.crossunder(close, ps2_std) and is_bearish_confirmation()
sell_signal_fps1 = enable_signals and enable_fibonacci and ta.crossunder(close, ps1_fib) and is_bearish_confirmation()
sell_signal_fps2 = enable_signals and enable_fibonacci and ta.crossunder(close, ps2_fib) and is_bearish_confirmation()

// CPP breakout signals (neutral trend signals)
cpp_bullish_breakout = enable_signals and enable_cpp and ta.crossover(close, cpp) and is_bullish_confirmation()
cpp_bearish_breakout = enable_signals and enable_cpp and ta.crossunder(close, cpp) and is_bearish_confirmation()

// Combined signals
any_buy_signal = buy_signal_pr1 or buy_signal_pr2 or buy_signal_fpr1 or buy_signal_fpr2 or cpp_bullish_breakout
any_sell_signal = sell_signal_ps1 or sell_signal_ps2 or sell_signal_fps1 or sell_signal_fps2 or cpp_bearish_breakout

// ==================== SIGNAL PLOTTING ====================
// Plot buy/sell arrows
plotshape(any_buy_signal, title="Buy Signal", location=location.belowbar, color=color.green,
    style=shape.labelup, text="BUY", textcolor=color.white, size=size.small)

plotshape(any_sell_signal, title="Sell Signal", location=location.abovebar, color=color.red,
    style=shape.labeldown, text="SELL", textcolor=color.white, size=size.small)

// Plot CPP breakout signals
plotshape(cpp_bullish_breakout, title="CPP Bullish", location=location.belowbar, color=color.blue,
    style=shape.triangleup, size=size.tiny)

plotshape(cpp_bearish_breakout, title="CPP Bearish", location=location.abovebar, color=color.blue,
    style=shape.triangledown, size=size.tiny)

// ==================== NEXT DAY LEVELS ====================
// Calculate and plot next day levels if enabled
if show_next_day and barstate.islast
    // Get current day's data for next day calculation
    [curr_open, curr_high, curr_low, curr_close] = request.security(syminfo.tickerid, get_timeframe(),
        [open, high, low, close], barmerge.gaps_off, barmerge.lookahead_off)

    // Next day pivot calculations
    next_cpp = (curr_high + curr_low + curr_close) / 3
    next_pr1 = 2 * next_cpp - curr_low
    next_ps1 = 2 * next_cpp - curr_high

    // Plot next day levels as dashed lines
    line.new(bar_index, next_cpp, bar_index + 20, next_cpp, color=color.yellow, width=2, style=line.style_dashed)
    line.new(bar_index, next_pr1, bar_index + 20, next_pr1, color=color.orange, width=1, style=line.style_dashed)
    line.new(bar_index, next_ps1, bar_index + 20, next_ps1, color=color.orange, width=1, style=line.style_dashed)

    // Labels for next day levels
    label.new(bar_index + 10, next_cpp, "Next CPP", color=color.yellow, style=label.style_label_right, textcolor=color.black, size=size.small)
    label.new(bar_index + 10, next_pr1, "Next PR1", color=color.orange, style=label.style_label_right, textcolor=color.white, size=size.small)
    label.new(bar_index + 10, next_ps1, "Next PS1", color=color.orange, style=label.style_label_right, textcolor=color.white, size=size.small)

// ==================== ALERTS ====================
// Alert conditions for strategy automation
alertcondition(enable_alerts and any_buy_signal, title="Buy Signal Alert",
    message="🟢 BUY SIGNAL: Price broke above pivot resistance level")

alertcondition(enable_alerts and any_sell_signal, title="Sell Signal Alert",
    message="🔴 SELL SIGNAL: Price broke below pivot support level")

alertcondition(enable_alerts and cpp_bullish_breakout, title="CPP Bullish Breakout",
    message="🔵 CPP BULLISH: Price broke above Center Pivot Point")

alertcondition(enable_alerts and cpp_bearish_breakout, title="CPP Bearish Breakout",
    message="🔵 CPP BEARISH: Price broke below Center Pivot Point")

// Specific level alerts
alertcondition(enable_alerts and buy_signal_pr1, title="PR1 Buy Signal",
    message="📈 BUY: Price broke above PR1 resistance")

alertcondition(enable_alerts and sell_signal_ps1, title="PS1 Sell Signal",
    message="📉 SELL: Price broke below PS1 support")

// ==================== CLEANUP & OPTIMIZATION ====================
// Clean up old drawings to reduce chart clutter
var int max_drawings = 100

// Clean up boxes periodically
if barstate.islast and bar_index % 50 == 0
    all_boxes = box.all
    if array.size(all_boxes) > max_drawings
        for i = 0 to array.size(all_boxes) - max_drawings - 1
            box.delete(array.get(all_boxes, i))

// Clean up labels periodically
if barstate.islast and bar_index % 50 == 0
    all_labels = label.all
    if array.size(all_labels) > max_drawings
        for i = 0 to array.size(all_labels) - max_drawings - 1
            label.delete(array.get(all_labels, i))

// Clean up lines periodically
if barstate.islast and bar_index % 50 == 0
    all_lines = line.all
    if array.size(all_lines) > max_drawings
        for i = 0 to array.size(all_lines) - max_drawings - 1
            line.delete(array.get(all_lines, i))

// ==================== TABLE DISPLAY (OPTIONAL) ====================
// Display current pivot levels in a table
if show_labels and barstate.islast
    var table info_table = table.new(position.top_right, 3, 8, bgcolor=color.white, border_width=1)

    // Table headers
    table.cell(info_table, 0, 0, "Level", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 0, "Standard", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 2, 0, "Fibonacci", text_color=color.black, bgcolor=color.gray)

    // Resistance levels
    table.cell(info_table, 0, 1, "PR3", text_color=color.red)
    table.cell(info_table, 1, 1, str.tostring(pr3_std, "#.##"), text_color=color.black)
    table.cell(info_table, 2, 1, str.tostring(pr3_fib, "#.##"), text_color=color.black)

    table.cell(info_table, 0, 2, "PR2", text_color=color.red)
    table.cell(info_table, 1, 2, str.tostring(pr2_std, "#.##"), text_color=color.black)
    table.cell(info_table, 2, 2, str.tostring(pr2_fib, "#.##"), text_color=color.black)

    table.cell(info_table, 0, 3, "PR1", text_color=color.red)
    table.cell(info_table, 1, 3, str.tostring(pr1_std, "#.##"), text_color=color.black)
    table.cell(info_table, 2, 3, str.tostring(pr1_fib, "#.##"), text_color=color.black)

    // CPP
    table.cell(info_table, 0, 4, "CPP", text_color=color.blue)
    table.cell(info_table, 1, 4, str.tostring(cpp, "#.##"), text_color=color.black)
    table.cell(info_table, 2, 4, str.tostring(cpp, "#.##"), text_color=color.black)

    // Support levels
    table.cell(info_table, 0, 5, "PS1", text_color=color.green)
    table.cell(info_table, 1, 5, str.tostring(ps1_std, "#.##"), text_color=color.black)
    table.cell(info_table, 2, 5, str.tostring(ps1_fib, "#.##"), text_color=color.black)

    table.cell(info_table, 0, 6, "PS2", text_color=color.green)
    table.cell(info_table, 1, 6, str.tostring(ps2_std, "#.##"), text_color=color.black)
    table.cell(info_table, 2, 6, str.tostring(ps2_fib, "#.##"), text_color=color.black)

    table.cell(info_table, 0, 7, "PS3", text_color=color.green)
    table.cell(info_table, 1, 7, str.tostring(ps3_std, "#.##"), text_color=color.black)
    table.cell(info_table, 2, 7, str.tostring(ps3_fib, "#.##"), text_color=color.black)
